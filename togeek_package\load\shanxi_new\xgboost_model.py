import numpy as np
import pandas as pd
import xgboost as xgb
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import GridSearchCV, TimeSeriesSplit
import time
import yaml
import joblib
from typing import Optional
from my_basic_model import MyBasicModel
from data_loader import DataPreprocessor


class XGBoostModel(MyBasicModel):
    """XGBoost时间序列预测模型"""
    
    @staticmethod
    def load_config(config_path: str = "config.yaml") -> dict:
        """加载配置文件"""
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
            print("✅ XGBoost配置加载成功")
            return config
        except FileNotFoundError:
            print("⚠️ 配置文件未找到，使用默认配置")
            return {
                "xgboost_params": {
                    "n_estimators": 100,
                    "max_depth": 6,
                    "learning_rate": 0.1,
                    "subsample": 0.8,
                    "colsample_bytree": 0.8,
                    "random_state": 42,
                    "n_jobs": -1,
                    "early_stopping_rounds": 10,
                    "eval_metric": "rmse"
                }
            }
    
    @staticmethod
    def prepare_total_load_data(start_date: str, end_date: str, total_load_df: pd.DataFrame, config: dict):
        """
        准备总负荷数据用于XGBoost训练
        
        :param start_date: 开始日期
        :param end_date: 结束日期  
        :param total_load_df: 总负荷数据DataFrame
        :param config: 配置字典
        :return: 特征数据X_df, 目标数据y_series, 日期dates, 预处理器preprocessor
        """
        print(f"🔧 开始XGBoost数据预处理...")
        print(f"   时间范围: {start_date} 到 {end_date}")
        
        # 创建数据预处理器
        preprocessor = DataPreprocessor(
            start_date=start_date,
            end_date=end_date,
            excel_file_path="山西零售用户负荷.xlsx",
            weather_csv_path="weather_山西.csv"
        )
        
        # 使用总负荷数据进行特征工程
        X_df, y_series, dates = preprocessor.create_features_from_total_load(
            total_load_df, 
            target_column='total_load',
            datetime_column='datetime'
        )
        
        print(f"特征工程完成: {X_df.shape[1]} 个特征，{len(y_series)} 个样本")
        
        return X_df, y_series, dates, preprocessor
    
    @classmethod
    def create_and_train(cls, X_train, y_train, config=None):
        """
        创建并训练XGBoost模型
        
        :param X_train: 训练特征
        :param y_train: 训练目标
        :param config: 配置字典
        :return: 训练好的模型实例
        """
        if config is None:
            config = cls.load_config()
        
        # 获取XGBoost参数
        xgb_params = config.get("xgboost_params", {
            "n_estimators": 100,
            "max_depth": 6,
            "learning_rate": 0.1,
            "subsample": 0.8,
            "colsample_bytree": 0.8,
            "random_state": 42,
            "n_jobs": -1,
            "early_stopping_rounds": 10,
            "eval_metric": "rmse"
        })
        
        # 创建并训练模型
        xgb_model = cls(**xgb_params)
        xgb_model.train(X_train, y_train)
        
        return xgb_model
    
    def __init__(self, n_estimators: int = 100, max_depth: int = 6, learning_rate: float = 0.1,
                 subsample: float = 0.8, colsample_bytree: float = 0.8, random_state: int = 42,
                 n_jobs: int = -1, early_stopping_rounds: int = 10, eval_metric: str = "rmse"):
        """
        初始化XGBoost模型
        
        参数:
            n_estimators: 树的数量
            max_depth: 树的最大深度
            learning_rate: 学习率
            subsample: 样本采样比例
            colsample_bytree: 特征采样比例
            random_state: 随机种子
            n_jobs: 并行作业数
            early_stopping_rounds: 早停轮数
            eval_metric: 评估指标
        """
        super().__init__()
        
        # 参数验证
        if n_estimators <= 0:
            raise ValueError(f"n_estimators必须大于0，当前值: {n_estimators}")
        if max_depth <= 0:
            raise ValueError(f"max_depth必须大于0，当前值: {max_depth}")
        if not (0 < learning_rate <= 1):
            raise ValueError(f"learning_rate必须在(0,1]之间，当前值: {learning_rate}")
        if not (0 < subsample <= 1):
            raise ValueError(f"subsample必须在(0,1]之间，当前值: {subsample}")
        if not (0 < colsample_bytree <= 1):
            raise ValueError(f"colsample_bytree必须在(0,1]之间，当前值: {colsample_bytree}")
        
        self.n_estimators = n_estimators
        self.max_depth = max_depth
        self.learning_rate = learning_rate
        self.subsample = subsample
        self.colsample_bytree = colsample_bytree
        self.random_state = random_state
        self.n_jobs = n_jobs
        self.early_stopping_rounds = early_stopping_rounds
        self.eval_metric = eval_metric
        
        # 模型组件
        self.model = None
        self.scaler_X = MinMaxScaler()
        self.scaler_y = MinMaxScaler()
        self.feature_columns = None
        self.feature_importance_ = None
        
        print(f"XGBoost模型初始化完成")
        print(f"   参数: n_estimators={n_estimators}, max_depth={max_depth}, lr={learning_rate}")
    
    def train(self, X_train: pd.DataFrame, y_train: pd.Series):
        """训练XGBoost模型"""
        print(f"开始训练XGBoost模型，训练样本数: {len(X_train)}")
        train_start = time.time()
        
        # 保存特征列名
        self.feature_columns = X_train.columns.tolist()
        
        # 数据标准化
        X_scaled = self.scaler_X.fit_transform(X_train)
        y_values = np.array(y_train).reshape(-1, 1)
        y_scaled = self.scaler_y.fit_transform(y_values).flatten()
        
        # 创建XGBoost模型
        self.model = xgb.XGBRegressor(
            n_estimators=self.n_estimators,
            max_depth=self.max_depth,
            learning_rate=self.learning_rate,
            subsample=self.subsample,
            colsample_bytree=self.colsample_bytree,
            random_state=self.random_state,
            n_jobs=self.n_jobs,
            eval_metric=self.eval_metric
        )
        
        # 训练模型（使用早停）
        eval_set = [(X_scaled, y_scaled)]
        self.model.fit(
            X_scaled, y_scaled,
            eval_set=eval_set,
            early_stopping_rounds=self.early_stopping_rounds,
            verbose=False
        )
        
        # 保存特征重要性
        self.feature_importance_ = self.model.feature_importances_
        
        # 保存最佳模型
        joblib.dump(self.model, 'best_xgboost_model.pkl')
        
        train_time = time.time() - train_start
        print(f"XGBoost训练完成，耗时: {train_time:.2f} 秒")
        print(f"最佳迭代轮数: {self.model.best_iteration}")
        
        # 显示前10个最重要的特征
        if self.feature_importance_ is not None and self.feature_columns is not None:
            feature_importance_df = pd.DataFrame({
                'feature': self.feature_columns,
                'importance': self.feature_importance_
            }).sort_values('importance', ascending=False)
            
            print(f"\n📊 前10个最重要特征:")
            for i, (_, row) in enumerate(feature_importance_df.head(10).iterrows()):
                print(f"   {i+1:2d}. {row['feature']:<25} {row['importance']:.4f}")
    
    def predict(self, X_test: pd.DataFrame, y_true=None, dates=None) -> pd.DataFrame:
        """
        预测
        
        :param X_test: 测试特征数据
        :param y_true: 真实值（用于训练集预测时的长度补全）
        :param dates: 日期数据（用于显示补全信息）
        :return: 预测结果DataFrame
        """
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用train()方法")
        
        print(f"开始XGBoost预测，样本数: {len(X_test)}")
        pred_start = time.time()
        
        # 确保特征顺序一致
        X_test_filtered = X_test[self.feature_columns]
        
        # 确保返回的是DataFrame类型
        if isinstance(X_test_filtered, pd.Series):
            X_test_filtered = X_test_filtered.to_frame().T
        
        assert isinstance(X_test_filtered, pd.DataFrame), "X_test必须是DataFrame类型"
        
        # 数据标准化
        X_scaled = self.scaler_X.transform(X_test_filtered)
        
        # 预测
        predictions_scaled = self.model.predict(X_scaled)
        
        # 反标准化
        predictions = self.scaler_y.inverse_transform(predictions_scaled.reshape(-1, 1)).flatten()
        
        # 确保非负
        predictions = np.maximum(predictions, 0)
        
        pred_time = time.time() - pred_start
        print(f"XGBoost预测完成，耗时: {pred_time:.2f} 秒")
        
        return pd.DataFrame({'value': predictions})
    
    def calculate_accuracy(self, y_true, y_pred, threshold=0.1):
        """
        计算预测准确率（基于相对误差的准确率）
        
        :param y_true: 真实值
        :param y_pred: 预测值
        :param threshold: 接近零值的阈值
        :return: 准确率 (0-1之间)
        """
        y_true = np.array(y_true)
        y_pred = np.array(y_pred)
        
        # 初始化准确率数组
        accuracies = np.zeros_like(y_true, dtype=float)
        
        # 对于接近零的真实值，如果预测值也接近零，则认为准确率为1
        near_zero_mask = np.abs(y_true) <= threshold
        near_zero_pred_mask = np.abs(y_pred) <= threshold
        accuracies[near_zero_mask & near_zero_pred_mask] = 1.0
        
        # 对于非零值，使用MAPE的倒数作为准确率
        non_zero_mask = ~near_zero_mask
        if np.sum(non_zero_mask) > 0:
            # 计算相对误差
            relative_errors = np.abs((y_true[non_zero_mask] - y_pred[non_zero_mask]) / y_true[non_zero_mask])
            # 将相对误差转换为准确率：准确率 = 1 - 相对误差
            accuracies[non_zero_mask] = np.maximum(0, 1 - relative_errors)
        
        # 返回平均准确率
        mean_accuracy = np.mean(accuracies)
        return mean_accuracy
    
    def save(self, path: str):
        """保存模型"""
        if self.model is None:
            raise ValueError("模型尚未训练，无法保存")
        
        model_data = {
            'model': self.model,
            'scaler_X': self.scaler_X,
            'scaler_y': self.scaler_y,
            'feature_columns': self.feature_columns,
            'feature_importance': self.feature_importance_,
            'params': {
                'n_estimators': self.n_estimators,
                'max_depth': self.max_depth,
                'learning_rate': self.learning_rate,
                'subsample': self.subsample,
                'colsample_bytree': self.colsample_bytree,
                'random_state': self.random_state,
                'n_jobs': self.n_jobs,
                'early_stopping_rounds': self.early_stopping_rounds,
                'eval_metric': self.eval_metric
            }
        }
        
        joblib.dump(model_data, path)
        print(f"💾 XGBoost模型已保存至: {path}")
    
    def load(self, path: str):
        """加载模型"""
        try:
            model_data = joblib.load(path)
            
            self.model = model_data['model']
            self.scaler_X = model_data['scaler_X']
            self.scaler_y = model_data['scaler_y']
            self.feature_columns = model_data['feature_columns']
            self.feature_importance_ = model_data.get('feature_importance', None)
            
            # 恢复参数
            params = model_data.get('params', {})
            self.n_estimators = params.get('n_estimators', self.n_estimators)
            self.max_depth = params.get('max_depth', self.max_depth)
            self.learning_rate = params.get('learning_rate', self.learning_rate)
            self.subsample = params.get('subsample', self.subsample)
            self.colsample_bytree = params.get('colsample_bytree', self.colsample_bytree)
            self.random_state = params.get('random_state', self.random_state)
            self.n_jobs = params.get('n_jobs', self.n_jobs)
            self.early_stopping_rounds = params.get('early_stopping_rounds', self.early_stopping_rounds)
            self.eval_metric = params.get('eval_metric', self.eval_metric)
            
            print(f"✅ XGBoost模型已从 {path} 加载")
            
        except Exception as e:
            print(f"❌ 加载模型失败: {str(e)}")
            raise
