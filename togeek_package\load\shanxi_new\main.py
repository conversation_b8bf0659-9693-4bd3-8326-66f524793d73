import pandas as pd
import glob
from time_series_plot import TimeSeriesPlotter

# 导入自定义模块
from lstm_model import LSTMModel  # LSTM模型
# from data_loader import aggregate_all_users_load  # 汇总所有用户负荷函数


class LoadPredictionSystem:
    """山西零售用户负荷汇总与预测系统"""

    def __init__(self, load_data, weather_data, start_date="2025-05-01", end_date="2025-07-17",
                 end_date_for_predict="2025-07-12", pred_date="2025-07-16"):
        """
        初始化模型，接受列式字典格式的数据
        :param load_data: {'timestamp': [...], '实测值': [...]}
        :param weather_data: {'timestamp': [...], 't_2m': [...], ...}
        """

        self.total_load_df = load_data
        self.weather_data = weather_data

        self.start_date = start_date
        self.end_date = end_date
        self.end_date_for_predict = end_date_for_predict
        self.pred_date = pred_date

        # 初始化组件
        self.lstm_model = None
        self.plotter = TimeSeriesPlotter()
        print("🚀 山西零售用户负荷预测开始")
        print("=" * 60)
        
    def prepare_data(self):
        """准备训练和测试数据"""
        if self.total_load_df is None:
            raise ValueError("请先加载总负荷数据")

        # 加载模型参数
        config = LSTMModel.load_config()

        # 将传入的字典数据转换为DataFrame格式
        total_load_df = pd.DataFrame(self.total_load_df)

        # 直接使用传入的数据进行特征工程
        # 由于数据已经是筛选好的，我们使用虚拟的日期范围来满足接口要求
        X_df, y_series, dates, preprocessor = LSTMModel.prepare_total_load_data(
            self.start_date, self.end_date, total_load_df, config
        )

        # 转换日期为datetime
        end_date_for_predict_dt = pd.to_datetime(self.end_date_for_predict)
        pred_date_dt = pd.to_datetime(self.pred_date)

        # 找到训练集结束时间点 (end_date_for_predict 23:45)
        train_end_time = end_date_for_predict_dt + pd.Timedelta(hours=23, minutes=45)

        # 找到pred_date这一天的开始和结束时间
        pred_day_start = pred_date_dt
        pred_day_end = pred_date_dt + pd.Timedelta(hours=23, minutes=45)

        # 创建完整的数据框
        merged_df = pd.DataFrame({'dateTime': dates, 'load': y_series})
        for col in X_df.columns:
            merged_df[col] = X_df[col]

        # 按时间划分
        train_mask = merged_df['dateTime'] <= train_end_time
        test_mask = (merged_df['dateTime'] > train_end_time) & (merged_df['dateTime'] <= pred_day_end)
        pred_day_mask = (merged_df['dateTime'] >= pred_day_start) & (merged_df['dateTime'] <= pred_day_end)

        # 划分训练集
        train_data = merged_df[train_mask]
        X_train = train_data.drop(columns=['load', 'dateTime'])
        y_train = train_data['load']
        dates_train = train_data['dateTime'].tolist()

        # 划分测试集 (从训练集结束后到pred_date结束)
        test_data = merged_df[test_mask]
        X_test = test_data.drop(columns=['load', 'dateTime'])
        y_test = test_data['load']
        dates_test = test_data['dateTime'].tolist()

        # 提取pred_date这一天的数据用于最终评估
        pred_day_data = merged_df[pred_day_mask]
        X_pred_day = pred_day_data.drop(columns=['load', 'dateTime'])
        y_pred_day = pred_day_data['load']
        dates_pred_day = pred_day_data['dateTime'].tolist()

        print(f"\n📊 数据划分结果:")
        print(f"   训练集: {len(X_train)} 样本")
        print(f"   测试集: {len(X_test)} 样本")
        print(f"   目标评估日: {len(X_pred_day)} 样本")

        print(f"\n⏰ 时间范围详情:")
        print(f"   📈 训练集时间范围:")
        print(f"      起始时间: {dates_train[0]}")
        print(f"      结束时间: {dates_train[-1]}")
        print(f"      时间跨度: {pd.to_datetime(dates_train[-1]) - pd.to_datetime(dates_train[0])}")

        print(f"   🔮 测试集时间范围:")
        print(f"      起始时间: {dates_test[0]}")
        print(f"      结束时间: {dates_test[-1]}")
        print(f"      时间跨度: {pd.to_datetime(dates_test[-1]) - pd.to_datetime(dates_test[0])}")

        print(f"   🎯 目标评估日时间范围:")
        print(f"      起始时间: {dates_pred_day[0]}")
        print(f"      结束时间: {dates_pred_day[-1]}")
        print(f"      时间跨度: {pd.to_datetime(dates_pred_day[-1]) - pd.to_datetime(dates_pred_day[0])}")

        if len(X_pred_day) != 96:
            print(f"⚠️ 警告: 目标评估日样本数为 {len(X_pred_day)}，预期为96个")
        else:
            print(f"✅ 目标评估日包含完整的96个时间点")

        return {
            'X_train': X_train, 'y_train': y_train, 'dates_train': dates_train,
            'X_test': X_test, 'y_test': y_test, 'dates_test': dates_test,
            'X_pred_day': X_pred_day, 'y_pred_day': y_pred_day, 'dates_pred_day': dates_pred_day,
            'merged_df': merged_df, 'config': config,
            'end_date_for_predict_dt': end_date_for_predict_dt, 'pred_date_dt': pred_date_dt
        }

    def train_and_predict(self, data_dict):
        """训练模型并进行预测"""
        # 解包数据
        X_train = data_dict['X_train']
        y_train = data_dict['y_train']
        dates_train = data_dict['dates_train']
        X_test = data_dict['X_test']
        y_test = data_dict['y_test']
        dates_test = data_dict['dates_test']
        X_pred_day = data_dict['X_pred_day']
        y_pred_day = data_dict['y_pred_day']
        dates_pred_day = data_dict['dates_pred_day']
        merged_df = data_dict['merged_df']
        config = data_dict['config']
        end_date_for_predict_dt = data_dict['end_date_for_predict_dt']
        pred_date_dt = data_dict['pred_date_dt']

        # 训练LSTM模型
        self.lstm_model = LSTMModel.create_and_train(X_train, y_train, config)

        # 创建一个虚拟的y_test_zeros，因为我们不想在预测过程中使用真实负荷值
        y_test_zeros = y_test * 0  # 全部设为0，表示没有真实负荷数据

        # 预测（不传入真实负荷数据，模拟真实预测场景）
        test_predictions = self.lstm_model.predict_multistep_real_scenario(X_train, X_test, y_test_zeros, end_date_for_predict_dt, y_train)['value']
        train_predictions = self.lstm_model.predict(X_train, y_train, dates_train)['value']

        # 从测试集预测结果中提取pred_date这一天的预测值
        # 找到pred_date在测试集中的索引范围
        test_dates_series = pd.Series(dates_test)
        pred_day_start_idx = None
        pred_day_end_idx = None

        for i, date in enumerate(test_dates_series):
            if pd.to_datetime(date).date() == pred_date_dt.date():
                if pred_day_start_idx is None:
                    pred_day_start_idx = i
                pred_day_end_idx = i

        if pred_day_start_idx is not None and pred_day_end_idx is not None:
            # 提取pred_date这一天的预测结果
            pred_day_predictions = test_predictions[pred_day_start_idx:pred_day_end_idx+1]

            print(f"\n🎯 {self.pred_date} 预测结果提取:")
            print(f"   在测试集中的索引范围: {pred_day_start_idx} 到 {pred_day_end_idx}")
            print(f"   提取的预测值数量: {len(pred_day_predictions)}")

            # 计算pred_date这一天的准确率
            accuracy = self.lstm_model.calculate_accuracy(y_pred_day, pred_day_predictions)
            print(f"\n🏆 {self.pred_date} 当天预测准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")

            # 保存pred_date这一天的详细结果
            pred_day_results_df = pd.DataFrame({
                'datetime': dates_pred_day,
                'actual_total_load': y_pred_day.reset_index(drop=True),
                'predicted_total_load': pred_day_predictions.reset_index(drop=True)
            })
            pred_day_results_df.to_csv(f"总负荷_LSTM_{self.pred_date}_预测结果.csv", index=False, encoding='utf-8-sig')
            print(f"💾 {self.pred_date} 详细预测结果已保存至: 总负荷_LSTM_{self.pred_date}_预测结果.csv")
        else:
            print(f"❌ 错误: 在测试集中未找到 {self.pred_date} 的数据")
            accuracy = 0.0

        # 获取真实的测试集负荷数据用于画图和计算准确率
        # 重新从原始数据中提取真实的y_test
        test_mask = (merged_df['dateTime'] > end_date_for_predict_dt + pd.Timedelta(hours=23, minutes=45)) & \
                   (merged_df['dateTime'] <= pred_date_dt + pd.Timedelta(hours=23, minutes=45))
        test_data_real = merged_df[test_mask]
        y_test_real = test_data_real['load']  # 真实的测试集负荷数据

        print(f"\n📊 使用真实负荷数据进行评估和画图:")
        print(f"   真实y_test范围: {y_test_real.min():.2f} - {y_test_real.max():.2f} kW")
        print(f"   真实y_test均值: {y_test_real.mean():.2f} kW")

        # 保存完整测试集结果和生成图表（使用真实负荷数据）
        test_results_df = pd.DataFrame({
            'datetime': dates_test,
            'actual_total_load': y_test_real.reset_index(drop=True),
            'predicted_total_load': test_predictions.reset_index(drop=True)
        })
        test_results_df.to_csv("总负荷_LSTM_预测结果.csv", index=False, encoding='utf-8-sig')

        self.plotter.plot_all_charts_and_calculate_metrics(
            dates_train, y_train, train_predictions,
            dates_test, y_test_real, test_predictions,  # 使用真实的y_test_real
            "总负荷_LSTM"
        )

        # 显示完整测试集的准确率（使用真实负荷数据计算）
        full_test_accuracy = self.lstm_model.calculate_accuracy(y_test_real, test_predictions)
        print(f"\n📊 完整测试集预测准确率: {full_test_accuracy:.4f} ({full_test_accuracy*100:.2f}%)")
        print(f"🎯 目标日期 {self.pred_date} 预测准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")

        return {
            'accuracy': accuracy,
            'full_test_accuracy': full_test_accuracy,
            'test_predictions': test_predictions,
            'train_predictions': train_predictions
        }

    def run(self):
        """运行完整的预测流程"""
        print(f"\n{'='*60}")
        print(f"开始总负荷LSTM预测")
        print(f"{'='*60}")

        try:
            # 1. 数据已经在初始化时传入，直接准备数据
            data_dict = self.prepare_data()

            # 2. 训练和预测
            results = self.train_and_predict(data_dict)

            print(f"\n🎉 预测完成！")
            print(f"📊 最终结果:")
            print(f"   目标日期准确率: {results['accuracy']:.4f} ({results['accuracy']*100:.2f}%)")
            print(f"   完整测试集准确率: {results['full_test_accuracy']:.4f} ({results['full_test_accuracy']*100:.2f}%)")

            return True

        except Exception as e:
            print(f"❌ 预测失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数 - 创建并运行预测系统"""
    # 示例数据 - 实际使用时应该从外部传入
    load_data = {
        'timestamp': [],  # 时间戳列表
        '实测值': []      # 负荷实测值列表
    }

    weather_data = {
        'timestamp': [],  # 时间戳列表
        't_2m': [],       # 温度数据
        # 其他天气特征...
    }

    # 创建预测系统实例
    system = LoadPredictionSystem(
        load_data=load_data,
        weather_data=weather_data,
        start_date="2025-05-01",
        end_date="2025-07-17",
        end_date_for_predict="2025-07-12",
        pred_date="2025-07-16"
    )

    # 运行预测
    system.run()


if __name__ == "__main__":
    main()