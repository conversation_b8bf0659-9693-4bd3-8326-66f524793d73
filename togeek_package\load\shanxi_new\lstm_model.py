import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
import time
import yaml
from typing import Optional
from my_basic_model import MyBasicModel
from data_loader import DataPreprocessor

class LSTMNet(nn.Module):
    """简单高效的LSTM神经网络模型"""
    def __init__(self, input_size, hidden_size = 64, num_layers = 2, dropout = 0.2):
        super(LSTMNet, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )

        # 全连接层
        self.fc = nn.Linear(hidden_size, 1)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # LSTM前向传播
        lstm_out, _ = self.lstm(x)

        # 取最后一个时间步的输出
        last_output = lstm_out[:, -1, :]

        # Dropout和全连接层
        output = self.dropout(last_output)
        output = self.fc(output)

        return output


class LSTMModel(MyBasicModel):
    """LSTM时间序列预测模型"""

    @staticmethod
    def auto_determine_sequence_length(y_data):
        """使用ACF自动确定序列长度，采用更实用的策略"""
        try:
            from statsmodels.tsa.stattools import acf

            # 准备数据
            data = y_data.values.flatten() if hasattr(y_data, 'values') else np.array(y_data).flatten()
            data = data[~np.isnan(data)]

            # 计算ACF
            max_lags = min(50, len(data) // 4)
            acf_values = acf(data, nlags=max_lags, alpha=0.05)
            acf_vals = acf_values[0] if isinstance(acf_values, tuple) else acf_values

            # 策略1：找到首次不显著的位置
            threshold = 1.96 / np.sqrt(len(data))
            for i in range(6, min(len(acf_vals), 49)):  # 限制在合理范围内
                if abs(acf_vals[i]) < threshold:
                    print(f"🎯 ACF自动定阶(显著性): {i}")
                    return i

            # 策略1没找到就用48
            print(f"🎯 ACF自动定阶(默认): 48")
            return 48

        except Exception as e:
            print(f"❌ ACF分析异常: {e}")

        print(f"📋 使用默认序列长度: 24")
        return 24

    @staticmethod
    def load_config(config_path: str = "config.yaml") -> dict:
        """加载配置文件"""
        with open(config_path, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
        print("✅ 配置加载成功")
        return config



    @staticmethod
    def prepare_total_load_data(start_date: str, end_date: str, total_load_df: pd.DataFrame, config: dict):
        """
        准备总负荷数据：包括数据加载、预处理和特征工程

        :param start_date: 开始日期
        :param end_date: 结束日期
        :param total_load_df: 总负荷DataFrame (包含dateTime和total_load列)
        :param config: 配置字典
        :return: (X_df, y_series, dates, preprocessor)
        """
        print("🔄 开始处理总负荷数据...")

        preprocessor = DataPreprocessor(start_date, end_date)
        preprocessor.load_freq = '15T'  # 设置负荷数据频率

        # 加载负荷数据与天气数据
        load_df = total_load_df.rename(columns={'total_load': 'load'})
        weather_df = preprocessor.load_weather_data()

        # 统一时间列名
        if 't_datetime_cst' in weather_df.columns:
            weather_df = weather_df.rename(columns={'t_datetime_cst': 'dateTime'})

        # 确保时间列数据类型一致
        load_df['dateTime'] = pd.to_datetime(load_df['dateTime'])
        weather_df['dateTime'] = pd.to_datetime(weather_df['dateTime'])

        # 添加时间和节假日特征工程
        print("🔧 添加时间和节假日特征...")
        weather_df = preprocessor.process_weather_features(weather_df)

        # 合并数据
        merged_df = pd.merge(load_df, weather_df, on='dateTime', how='inner')
        print(f"合并后数据: {len(merged_df)} 条记录")

        # 特征工程
        use_enhanced_features = config.get("use_enhanced_features", True)
        data_method = "增强时间序列" if use_enhanced_features else "普通时间序列"
        print(f"【数据处理方式】: {data_method}")

        if use_enhanced_features:
            X_df, y_series, dates = preprocessor.create_enhanced_time_series_data(merged_df)
        else:
            X_df, y_series, dates = preprocessor.create_simple_time_series_data(merged_df)

        print(f"特征工程完成: {X_df.shape[1]} 个特征，{len(y_series)} 个样本")

        return X_df, y_series, dates, preprocessor

    @classmethod
    def create_and_train(cls, X_train, y_train, config=None):
        """
        创建并训练LSTM模型

        :param X_train: 训练特征
        :param y_train: 训练目标
        :param config: 配置字典
        :return: 训练好的模型实例
        """
        if config is None:
            config = cls.load_config()

        # 获取LSTM参数并自动确定序列长度
        lstm_params = config.get("lstm_params", {
            "sequence_length": 24, "hidden_size": 64, "num_layers": 2, "dropout": 0.2,
            "learning_rate": 0.001, "epochs": 50, "batch_size": 32, "device": "auto",
            "early_stopping_patience": 10
        })

        # 优先使用配置文件中的序列长度，如果没有配置则使用ACF自动定阶
        config_sequence_length = config.get("lstm_params", {}).get("sequence_length", None)
        if config_sequence_length is not None:
            final_sequence_length = config_sequence_length
            print(f"使用配置文件序列长度: {final_sequence_length}")
        else:
            final_sequence_length = cls.auto_determine_sequence_length(y_train)
            print(f"使用ACF自动定阶序列长度: {final_sequence_length}")

        lstm_params["sequence_length"] = final_sequence_length

        # 创建并训练模型
        lstm_model = cls(**lstm_params)
        lstm_model.train(X_train, y_train)

        return lstm_model

    def __init__(self, sequence_length: int, hidden_size: int, num_layers: int,
                 dropout: float, learning_rate: float, epochs: int, batch_size: int,
                 device: str, early_stopping_patience: int):
        """
        初始化LSTM模型

        参数:
            所有参数都应该通过config.yaml传入，不再使用默认值
            sequence_length: 输入序列长度（时间步数）
            hidden_size: LSTM隐藏层大小
            num_layers: LSTM层数
            dropout: Dropout比率
            learning_rate: 学习率
            epochs: 训练轮数
            batch_size: 批次大小
            device: 计算设备 ('auto', 'cpu', 'cuda')
            early_stopping_patience: 早停耐心值
        """
        super().__init__()

        # 参数验证 - 确保参数合理性
        if sequence_length <= 0:
            raise ValueError(f"sequence_length必须大于0，当前值: {sequence_length}")
        if hidden_size <= 0:
            raise ValueError(f"hidden_size必须大于0，当前值: {hidden_size}")
        if num_layers <= 0:
            raise ValueError(f"num_layers必须大于0，当前值: {num_layers}")
        if not (0 <= dropout <= 1):
            raise ValueError(f"dropout必须在0-1之间，当前值: {dropout}")
        if learning_rate <= 0:
            raise ValueError(f"learning_rate必须大于0，当前值: {learning_rate}")
        if epochs <= 0:
            raise ValueError(f"epochs必须大于0，当前值: {epochs}")
        if batch_size <= 0:
            raise ValueError(f"batch_size必须大于0，当前值: {batch_size}")
        if early_stopping_patience <= 0:
            raise ValueError(f"early_stopping_patience必须大于0，当前值: {early_stopping_patience}")

        self.sequence_length = sequence_length
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.dropout = dropout
        self.learning_rate = learning_rate
        self.epochs = epochs
        self.batch_size = batch_size
        self.early_stopping_patience = early_stopping_patience

        # 设备配置
        if device == 'auto':
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        # 模型组件
        self.model = None
        self.scaler_X = MinMaxScaler()
        self.scaler_y = MinMaxScaler()
        self.feature_columns = None
        
        print(f"LSTM模型初始化完成，使用设备: {self.device}")

    def _prepare_sequences(self, X, y=None):
        """准备LSTM输入序列"""
        sequences = []
        targets = []
        
        for i in range(len(X) - self.sequence_length + 1):
            # 输入序列
            seq = X[i:i + self.sequence_length]
            sequences.append(seq)
            
            # 目标值（预测下一个时间点）
            if y is not None:
                target = y[i + self.sequence_length - 1]
                targets.append(target)
        
        sequences = np.array(sequences)
        if y is not None:
            targets = np.array(targets)
            return sequences, targets
        else:
            return sequences
    
    def train(self, X_train: pd.DataFrame, y_train: pd.Series):
        """训练LSTM模型"""
        print(f"开始训练LSTM模型，训练样本数: {len(X_train)}")
        train_start = time.time()

        # 设置随机种子确保结果可重复
        torch.manual_seed(42)
        np.random.seed(42)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(42)
        
        # 保存特征列名
        self.feature_columns = X_train.columns.tolist()
        
        # 数据标准化
        X_scaled = self.scaler_X.fit_transform(X_train)
        y_values = np.array(y_train).reshape(-1, 1)
        y_scaled = self.scaler_y.fit_transform(y_values).flatten()
        
        # 准备序列数据
        X_seq, y_seq = self._prepare_sequences(X_scaled, y_scaled)
        
        if len(X_seq) == 0:
            raise ValueError(f"数据长度不足以构建序列，需要至少{self.sequence_length}个样本")
        
        print(f"构建了 {len(X_seq)} 个训练序列，每个序列长度: {self.sequence_length}")
        
        # 转换为PyTorch张量
        X_tensor = torch.FloatTensor(X_seq).to(self.device)
        y_tensor = torch.FloatTensor(y_seq).to(self.device)
        
        # 创建数据加载器
        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size = self.batch_size, shuffle = True)
        
        # 初始化模型
        input_size = X_train.shape[1]
        self.model = LSTMNet(
            input_size = input_size,
            hidden_size = self.hidden_size,
            num_layers = self.num_layers,
            dropout = self.dropout
        ).to(self.device)
        
        # 损失函数和优化器
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(self.model.parameters(), lr = self.learning_rate)
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor = 0.5, patience = 5
        )
        
        # 训练循环
        best_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(self.epochs):
            self.model.train()
            total_loss = 0
            
            for batch_X, batch_y in dataloader:
                optimizer.zero_grad()
                
                # 前向传播
                outputs = self.model(batch_X).squeeze(-1)  # 只移除最后一个维度
                # 确保outputs和batch_y的维度匹配
                if outputs.dim() == 0:  # 如果outputs是标量
                    outputs = outputs.unsqueeze(0)
                if batch_y.dim() == 0:  # 如果batch_y是标量
                    batch_y = batch_y.unsqueeze(0)
                loss = criterion(outputs, batch_y)
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            avg_loss = total_loss / len(dataloader)
            scheduler.step(avg_loss)
            
            # 早停检查
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_lstm_model.pth')
            else:
                patience_counter += 1
            
            # 打印进度
            if (epoch + 1) % 20 == 0:
                print(f"Epoch [{epoch+1}/{self.epochs}], Loss: {avg_loss:.6f}")
            
            # 早停
            if patience_counter >= self.early_stopping_patience:
                print(f"早停触发，在第 {epoch+1} 轮停止训练")
                break
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_lstm_model.pth'))
        
        train_time = time.time() - train_start
        print(f"LSTM训练完成，耗时: {train_time:.2f} 秒")
    
    def predict(self, X_test: pd.DataFrame, y_true=None, dates=None) -> pd.DataFrame:
        """
        预测，自动处理序列长度不一致问题

        :param X_test: 测试特征数据
        :param y_true: 真实值（用于训练集预测时的长度补全）
        :param dates: 日期数据（用于显示补全信息）
        :return: 预测结果DataFrame
        """
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用train()方法")

        print(f"开始LSTM预测，样本数: {len(X_test)}")
        pred_start = time.time()

        # 确保特征顺序一致
        X_test_filtered = X_test[self.feature_columns]

        # 确保返回的是DataFrame类型
        if isinstance(X_test_filtered, pd.Series):
            X_test_filtered = X_test_filtered.to_frame().T

        assert isinstance(X_test_filtered, pd.DataFrame), "X_test必须是DataFrame类型"

        # 数据标准化
        X_scaled = self.scaler_X.transform(X_test_filtered)

        # 准备序列数据
        X_seq = self._prepare_sequences(X_scaled)

        if len(X_seq) == 0:
            raise ValueError(f"测试数据长度不足以构建序列，需要至少{self.sequence_length}个样本")

        # 转换为PyTorch张量
        X_tensor = torch.FloatTensor(X_seq).to(self.device)

        # 预测
        self.model.eval()
        predictions = []

        with torch.no_grad():
            for i in range(0, len(X_tensor), self.batch_size):
                batch = X_tensor[i:i + self.batch_size]
                outputs = self.model(batch)
                # 安全地处理输出维度
                output_np = outputs.cpu().numpy()
                if output_np.ndim > 1:
                    predictions.extend(output_np.flatten())
                else:
                    predictions.extend(output_np)

        # 反标准化
        predictions = np.array(predictions).reshape(-1, 1)
        predictions = self.scaler_y.inverse_transform(predictions).flatten()

        # 确保非负
        predictions = np.maximum(predictions, 0)

        # 处理序列长度不一致问题（主要用于训练集预测）
        missing_count = len(X_test) - len(predictions)
        if missing_count > 0 and y_true is not None:
            print(f"📊 由于序列长度限制，前{missing_count}个样本无法预测")
            print(f"💡 使用真实负荷数据填补前{missing_count}个样本的预测值")

            # 获取前missing_count个样本的真实负荷值
            real_values_for_missing = y_true.iloc[:missing_count] if hasattr(y_true, 'iloc') else y_true[:missing_count]

            # 转换为numpy数组以确保索引正常工作
            real_values_array = np.array(real_values_for_missing)

            # 合并：真实值 + 预测值
            full_predictions = np.concatenate([real_values_array, predictions])

            # 显示补全信息
            if dates is not None:
                missing_start_date = dates[0]
                missing_end_date = dates[missing_count - 1]
                print(f"⚠️  无法预测的日期范围: {missing_start_date} 至 {missing_end_date}")
                print(f"✅ 这些日期使用真实负荷数据: {real_values_array[0]:.3f} ~ {real_values_array[-1]:.3f}")

            predictions = full_predictions

        pred_time = time.time() - pred_start
        print(f"LSTM预测完成，耗时: {pred_time:.2f} 秒")

        return pd.DataFrame({'value': predictions})

    def predict_multistep(self, X_train: pd.DataFrame, X_test: pd.DataFrame, y_test: pd.Series, steps_per_day: int = 96) -> pd.DataFrame:
        """
        真实场景多步预测：每天预测96个时间点，使用递归预测

        改进的预测逻辑：
        - 每天开始前：使用前一天的真实负荷数据更新所有相关特征工程
        - 每天第1个时间点：基于更新后的真实历史信息进行预测
        - 每天第2个时间点：使用真实历史信息 + 第1个时间点的预测值
        - 每天第3个时间点：使用真实历史信息 + 第1、2个时间点的预测值
        - 依此类推...

        :param X_train: 训练集特征数据
        :param X_test: 测试集特征数据
        :param y_test: 测试集真实负荷数据（用于每天开始时更新特征）
        :param steps_per_day: 每天的时间点数量（默认96，即15分钟间隔）
        :return: 完整测试集的预测结果
        """
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用train()方法")

        print(f"开始LSTM真实场景多步预测")
        print(f"测试集样本数: {len(X_test)}, 每天时间点数: {steps_per_day}")
        pred_start = time.time()

        # 确保特征顺序一致
        X_train_filtered = X_train[self.feature_columns]
        X_test_filtered = X_test[self.feature_columns]

        # 确保返回的是DataFrame类型
        if isinstance(X_train_filtered, pd.Series):
            X_train_filtered = X_train_filtered.to_frame().T
        if isinstance(X_test_filtered, pd.Series):
            X_test_filtered = X_test_filtered.to_frame().T

        assert isinstance(X_train_filtered, pd.DataFrame), "X_train必须是DataFrame类型"
        assert isinstance(X_test_filtered, pd.DataFrame), "X_test必须是DataFrame类型"

        # 数据标准化
        X_train_scaled = self.scaler_X.transform(X_train_filtered)
        X_test_scaled = self.scaler_X.transform(X_test_filtered)

        # 获取训练集最后sequence_length个样本作为初始序列
        if len(X_train_scaled) < self.sequence_length:
            raise ValueError(f"训练集长度不足，需要至少{self.sequence_length}个样本")

        # 找到负荷特征在特征列表中的索引（假设负荷特征名包含'load'或者是第一个特征）
        load_feature_idx = None
        if self.feature_columns is not None:
            for idx, col in enumerate(self.feature_columns):
                if 'load' in col.lower():
                    load_feature_idx = idx
                    break
        if load_feature_idx is None:
            # 如果没找到包含'load'的特征，假设第一个特征是负荷
            load_feature_idx = 0
            print(f"警告：未找到负荷特征，假设第{load_feature_idx}个特征是负荷特征")

        predictions = []
        self.model.eval()

        # 计算总天数
        total_days = len(X_test) // steps_per_day
        remaining_steps = len(X_test) % steps_per_day

        print(f"预测 {total_days} 完整天 + {remaining_steps} 个剩余时间点")

        # 创建可修改的测试集副本，用于更新特征
        X_test_updated = X_test_filtered.copy()

        with torch.no_grad():
            for day in range(total_days + (1 if remaining_steps > 0 else 0)):
                # 计算当前天的起始和结束索引
                day_start = day * steps_per_day
                day_end = min((day + 1) * steps_per_day, len(X_test))
                current_day_steps = day_end - day_start

                print(f"预测第 {day + 1} 天，时间点 {day_start + 1}-{day_end}")

                # 每天开始前，使用前一天的真实负荷数据更新特征工程
                if day > 0:
                    print(f"   🔄 更新第 {day + 1} 天的特征工程（基于前一天真实负荷）")
                    self._update_features_with_real_load(X_test_updated, y_test, day_start, predictions)

                # 重新标准化更新后的特征
                X_test_scaled_updated = self.scaler_X.transform(X_test_updated)

                # 每天开始时，重新初始化序列
                if day == 0:
                    # 第一天：使用训练集最后的sequence_length个样本
                    current_sequence = X_train_scaled[-self.sequence_length:].copy()
                else:
                    # 后续天：使用前一天最后sequence_length个样本（已更新特征）
                    start_idx = max(0, day_start - self.sequence_length)

                    # 构建序列：使用更新后的特征数据
                    sequence_data = []
                    for seq_idx in range(start_idx, day_start):
                        if seq_idx < len(X_test_scaled_updated):
                            features = X_test_scaled_updated[seq_idx].copy()
                            sequence_data.append(features)

                    # 如果序列长度不够，用训练集数据补充
                    while len(sequence_data) < self.sequence_length:
                        sequence_data.insert(0, X_train_scaled[-(self.sequence_length - len(sequence_data))])

                    current_sequence = np.array(sequence_data[-self.sequence_length:])

                # 预测当前天的所有时间点
                for step in range(current_day_steps):
                    current_idx = day_start + step

                    # 获取当前时间点的特征（使用更新后的特征）
                    current_features = X_test_scaled_updated[current_idx].copy()

                    # 当天内的预测逻辑：
                    # - 第1个时间点：使用更新后的特征（基于前一天真实负荷）
                    # - 第2个时间点：使用第1个时间点的预测值
                    # - 第3个时间点：使用第2个时间点的预测值
                    # - 依此类推...
                    if step > 0:
                        # 使用前一个时间点的预测值替换当前时间点的负荷特征
                        prev_pred_scaled = self.scaler_y.transform(np.array([[predictions[current_idx - 1]]]))[0, 0]
                        current_features[load_feature_idx] = prev_pred_scaled
                    # 如果step == 0（每天第一个时间点），使用更新后的特征（已基于前一天真实负荷）

                    # 更新序列：移除最旧的样本，添加当前样本
                    current_sequence = np.vstack([current_sequence[1:], current_features.reshape(1, -1)])

                    # 将当前序列转换为张量并预测
                    seq_tensor = torch.FloatTensor(current_sequence).unsqueeze(0).to(self.device)
                    output = self.model(seq_tensor)

                    # 提取预测值
                    if output.dim() > 1:
                        pred_value = output.cpu().numpy()[0, 0]
                    else:
                        pred_value = output.cpu().numpy()[0]

                    predictions.append(pred_value)

        # 反标准化
        predictions = np.array(predictions).reshape(-1, 1)
        predictions = self.scaler_y.inverse_transform(predictions).flatten()

        # 确保非负
        predictions = np.maximum(predictions, 0)

        pred_time = time.time() - pred_start
        print(f"LSTM真实场景多步预测完成，耗时: {pred_time:.2f} 秒")
        print(f"预测了 {len(predictions)} 个时间点")

        return pd.DataFrame({'value': predictions})

    def predict_multistep_real_scenario(self, X_train: pd.DataFrame, X_test: pd.DataFrame, y_test_dummy: pd.Series, cutoff_date: pd.Timestamp, y_train: Optional[pd.Series] = None, steps_per_day: int = 96) -> pd.DataFrame:
        """
        真实场景多步预测：模拟只有cutoff_date之前的真实数据的情况

        预测逻辑：
        - cutoff_date之前：可以使用真实负荷数据更新特征
        - cutoff_date之后：所有负荷相关特征都使用预测值，不使用真实值

        :param X_train: 训练集特征数据
        :param X_test: 测试集特征数据
        :param y_test_dummy: 虚拟的测试集负荷数据（不使用，只为保持接口一致）
        :param cutoff_date: 真实数据的截止日期
        :param steps_per_day: 每天的时间点数量（默认96，即15分钟间隔）
        :return: 完整测试集的预测结果
        """
        # Note: y_test_dummy is intentionally unused as mentioned in docstring
        _ = y_test_dummy  # Suppress unused parameter warning

        print(f"🔮 开始真实场景预测，真实数据截止: {cutoff_date}")

        # 获取负荷特征的索引 - 更准确的查找方式
        load_feature_idx = None
        feature_columns = self.feature_columns if self.feature_columns else []

        # 优先查找load_lag_1特征
        for i, col in enumerate(feature_columns):
            if 'load_lag_1' in col:
                load_feature_idx = i
                print(f"✅ 找到负荷特征: {col} (索引: {i})")
                break

        if load_feature_idx is None:
            # 如果没找到，查找其他负荷相关特征
            for i, col in enumerate(feature_columns):
                if 'load' in col.lower():
                    load_feature_idx = i
                    print(f"⚠️ 使用备选负荷特征: {col} (索引: {i})")
                    break

        if load_feature_idx is None:
            print("❌ 错误: 未找到任何负荷特征！")
            if feature_columns:
                print(f"可用特征: {list(feature_columns[:10])}...")
            load_feature_idx = 0

        # 确保特征顺序一致
        X_train_filtered = X_train[self.feature_columns]
        X_test_filtered = X_test[self.feature_columns]

        # 确保返回的是DataFrame类型
        if isinstance(X_train_filtered, pd.Series):
            X_train_filtered = X_train_filtered.to_frame().T
        if isinstance(X_test_filtered, pd.Series):
            X_test_filtered = X_test_filtered.to_frame().T

        # 标准化特征
        X_train_scaled = self.scaler_X.transform(X_train_filtered)

        # 计算总天数和剩余步数
        total_steps = len(X_test)
        total_days = total_steps // steps_per_day
        remaining_steps = total_steps % steps_per_day

        print(f"预测 {total_days} 完整天 + {remaining_steps} 个剩余时间点")

        predictions = []

        # 创建可修改的测试集副本，用于更新特征
        X_test_updated = X_test_filtered.copy()

        # 🚨 关键修复：正确处理测试集中的负荷相关特征
        # 保留可以从训练集获得的真实负荷信息，清除基于测试集未来的信息
        print(f"🔧 正确处理测试集中的负荷相关特征...")
        self._prepare_real_scenario_features(X_test_updated, X_train, y_train, cutoff_date)

        with torch.no_grad():
            for day in range(total_days + (1 if remaining_steps > 0 else 0)):
                # 计算当前天的起始和结束索引
                day_start = day * steps_per_day
                day_end = min((day + 1) * steps_per_day, len(X_test))
                current_day_steps = day_end - day_start

                print(f"预测第 {day + 1} 天，时间点 {day_start + 1}-{day_end}")
                print(f"   🔮 使用预测值更新所有负荷相关特征")

                # 使用预测值更新特征工程
                if day > 0:  # 第一天不需要更新，因为可以使用训练集的真实数据
                    self._update_features_with_predictions(X_test_updated, day_start, predictions)

                # 重新标准化更新后的特征
                X_test_scaled_updated = self.scaler_X.transform(X_test_updated)

                # 每天开始时，重新初始化序列
                if day == 0:
                    # 第一天：使用训练集最后的sequence_length个样本
                    current_sequence = X_train_scaled[-self.sequence_length:].copy()
                else:
                    # 后续天：使用前一天最后sequence_length个样本（已更新特征）
                    start_idx = max(0, day_start - self.sequence_length)

                    # 构建序列：使用更新后的特征数据
                    sequence_data = []
                    for seq_idx in range(start_idx, day_start):
                        if seq_idx < len(X_test_scaled_updated):
                            features = X_test_scaled_updated[seq_idx].copy()
                            sequence_data.append(features)

                    # 如果序列长度不够，用训练集数据补充
                    while len(sequence_data) < self.sequence_length:
                        sequence_data.insert(0, X_train_scaled[-(self.sequence_length - len(sequence_data))])

                    current_sequence = np.array(sequence_data[-self.sequence_length:])

                # 预测当前天的所有时间点
                for step in range(current_day_steps):
                    current_idx = day_start + step

                    # 获取当前时间点的特征（使用更新后的特征）
                    current_features = X_test_scaled_updated[current_idx].copy()

                    # 当天内的预测逻辑：使用前一个时间点的预测值
                    if step > 0:
                        # 使用前一个时间点的预测值（已经是反标准化后的值）
                        prev_pred_value = predictions[current_idx - 1]

                        # 将预测值重新标准化后更新特征
                        prev_pred_scaled = self.scaler_y.transform(np.array([[prev_pred_value]]))[0, 0]
                        current_features[load_feature_idx] = prev_pred_scaled

                        # 调试信息
                        if current_idx <= day_start + 3:  # 只打印前几个时间点
                            print(f"      时间点{current_idx}: 使用前一预测值 {prev_pred_value:.2f} kW (标准化: {prev_pred_scaled:.4f})")

                    # 更新序列：移除最旧的样本，添加当前样本
                    current_sequence = np.vstack([current_sequence[1:], current_features.reshape(1, -1)])

                    # 将当前序列转换为张量并预测
                    seq_tensor = torch.FloatTensor(current_sequence).unsqueeze(0).to(self.device)
                    output = self.model(seq_tensor)

                    # 提取预测值（标准化后的值）
                    if output.dim() > 1:
                        pred_value_scaled = output.cpu().numpy()[0, 0]
                    else:
                        pred_value_scaled = output.cpu().numpy()[0]

                    # 立即反标准化得到真实预测值
                    pred_value_real = self.scaler_y.inverse_transform(np.array([[pred_value_scaled]]))[0, 0]
                    pred_value_real = max(pred_value_real, 0)  # 确保非负

                    predictions.append(pred_value_real)

                    # 调试信息
                    if current_idx <= day_start + 3:  # 只打印前几个时间点
                        print(f"      时间点{current_idx}: 预测值 {pred_value_real:.2f} kW (标准化: {pred_value_scaled:.4f})")

        print(f"真实场景预测完成，预测了 {len(predictions)} 个时间点")

        # predictions已经是反标准化后的真实值，不需要再次反标准化
        predictions = np.array(predictions)

        print(f"预测值范围: {predictions.min():.2f} - {predictions.max():.2f} kW")

        return pd.DataFrame({'value': predictions})

    def _clear_future_load_features(self, X_test_updated: pd.DataFrame, cutoff_date: pd.Timestamp):
        """
        清除测试集中所有基于未来真实负荷的特征
        只保留cutoff_date之前的负荷信息

        :param X_test_updated: 测试集特征数据
        :param cutoff_date: 真实数据的截止日期
        """

        print(f"   🔧 清除基于未来真实负荷的特征...")

        # 获取训练集最后几天的平均负荷作为初始值
        # 这里使用一个合理的初始负荷值（比如训练集的平均值）
        initial_load_value = 14.0  # 可以根据训练集统计调整

        # 清除所有负荷相关的滞后特征
        lag_features = ['load_lag_1', 'load_lag_2', 'load_lag_4', 'load_lag_8', 'load_lag_24', 'load_lag_48', 'load_lag_96']
        cleared_count = 0

        for lag_col in lag_features:
            if lag_col in X_test_updated.columns:
                # 将所有滞后特征设为初始值
                X_test_updated[lag_col] = initial_load_value
                cleared_count += 1

        # 清除所有基于负荷的滚动统计特征
        rolling_features = [col for col in X_test_updated.columns if 'rolling' in col or 'ewm' in col]
        for roll_col in rolling_features:
            if 'load' in roll_col.lower():
                X_test_updated[roll_col] = initial_load_value
                cleared_count += 1

        print(f"   ✅ 清除了{cleared_count}个基于未来真实负荷的特征")
        print(f"   📊 使用初始负荷值: {initial_load_value} kW")

    def _prepare_real_scenario_features(self, X_test_updated: pd.DataFrame, X_train: pd.DataFrame, y_train: Optional[pd.Series], cutoff_date: pd.Timestamp):
        """
        正确处理真实场景预测的特征：
        - 保留可以从训练集获得的真实负荷信息
        - 清除基于测试集未来的负荷信息

        :param X_test_updated: 测试集特征数据
        :param X_train: 训练集特征数据
        :param y_train: 训练集负荷数据
        :param cutoff_date: 真实数据的截止日期
        """

        print(f"   🔧 使用训练集真实负荷数据初始化滞后特征...")

        # 获取训练集最后的负荷值用于初始化滞后特征
        if y_train is not None and len(y_train) > 0:
            # 使用训练集最后96个值（最后一天）来初始化滞后特征
            train_last_values = y_train.tail(96).values

            # 更新滞后特征，使用训练集的真实值
            lag_features = ['load_lag_1', 'load_lag_2', 'load_lag_4', 'load_lag_8', 'load_lag_24', 'load_lag_48', 'load_lag_96']
            updated_count = 0

            for lag_col in lag_features:
                if lag_col in X_test_updated.columns:
                    lag_periods = int(lag_col.split('_')[-1])

                    # 对于测试集的每个时间点，计算应该使用的训练集值
                    for i in range(len(X_test_updated)):
                        # 计算对应的训练集索引
                        train_idx = len(y_train) - lag_periods + i

                        if train_idx >= 0 and train_idx < len(y_train):
                            # 使用训练集的真实值
                            X_test_updated.loc[X_test_updated.index[i], lag_col] = y_train.iloc[train_idx]
                            updated_count += 1
                        elif train_idx >= len(y_train):
                            # 超出训练集范围，使用训练集最后的值作为初始值
                            X_test_updated.loc[X_test_updated.index[i], lag_col] = y_train.iloc[-1]

            print(f"   ✅ 使用训练集真实值更新了滞后特征")
            print(f"   📊 训练集最后负荷值: {y_train.iloc[-1]:.2f} kW")
        else:
            print(f"   ⚠️ 未提供训练集负荷数据，使用默认值")

    def _update_features_with_predictions(self, X_test_updated: pd.DataFrame, day_start: int, predictions: list):
        """
        使用预测值更新特征工程（真实场景预测专用）

        :param X_test_updated: 可修改的测试集特征数据
        :param day_start: 当前天的起始索引
        :param predictions: 已有的预测结果
        """

        print(f"      🔄 更新第{day_start//96 + 1}天特征，已有{len(predictions)}个预测值")

        # 更新滞后特征
        lag_features = ['load_lag_1', 'load_lag_2', 'load_lag_4', 'load_lag_8', 'load_lag_24', 'load_lag_48', 'load_lag_96']
        updated_count = 0

        for i in range(day_start, min(day_start + 96, len(X_test_updated))):
            for lag_col in lag_features:
                if lag_col in X_test_updated.columns:
                    lag_periods = int(lag_col.split('_')[-1])
                    lag_idx = i - lag_periods

                    if lag_idx >= 0 and lag_idx < len(predictions):
                        # 使用预测值
                        old_value = X_test_updated.loc[X_test_updated.index[i], lag_col]
                        new_value = predictions[lag_idx]
                        X_test_updated.loc[X_test_updated.index[i], lag_col] = new_value
                        updated_count += 1

                        # 调试信息（只显示前几个更新）
                        if updated_count <= 3:
                            print(f"         {lag_col}: {old_value:.2f} → {new_value:.2f}")

        print(f"      ✅ 更新了{updated_count}个滞后特征值")

        # 更新滚动统计特征
        rolling_features = [col for col in X_test_updated.columns if 'rolling' in col or 'ewm' in col]

        for i in range(day_start, min(day_start + 96, len(X_test_updated))):
            for roll_col in rolling_features:
                if roll_col in X_test_updated.columns:
                    # 提取窗口大小
                    if 'rolling' in roll_col:
                        try:
                            window_size = int(roll_col.split('_')[-1])
                        except:
                            continue
                    elif 'ewm' in roll_col:
                        try:
                            window_size = int(roll_col.split('_')[-1])
                        except:
                            continue
                    else:
                        continue

                    # 计算窗口范围
                    window_start = max(0, i - window_size + 1)
                    window_end = i + 1

                    # 收集窗口内的预测负荷数据
                    window_data = []
                    for j in range(window_start, window_end):
                        if j < len(predictions):
                            window_data.append(predictions[j])

                    if len(window_data) > 0:
                        # 根据特征类型计算统计值
                        if 'mean' in roll_col:
                            X_test_updated.loc[X_test_updated.index[i], roll_col] = np.mean(window_data)
                        elif 'std' in roll_col:
                            X_test_updated.loc[X_test_updated.index[i], roll_col] = np.std(window_data) if len(window_data) > 1 else 0
                        elif 'max' in roll_col:
                            X_test_updated.loc[X_test_updated.index[i], roll_col] = np.max(window_data)
                        elif 'min' in roll_col:
                            X_test_updated.loc[X_test_updated.index[i], roll_col] = np.min(window_data)
                        elif 'ewm' in roll_col:
                            # 简化的指数加权移动平均
                            alpha = 2.0 / (window_size + 1)
                            ewm_val = window_data[0]
                            for val in window_data[1:]:
                                ewm_val = alpha * val + (1 - alpha) * ewm_val
                            X_test_updated.loc[X_test_updated.index[i], roll_col] = ewm_val

    def _update_features_with_real_load(self, X_test_updated: pd.DataFrame, y_test: pd.Series, day_start: int, predictions: list):
        """
        使用前一天的真实负荷数据更新特征工程

        :param X_test_updated: 可修改的测试集特征数据
        :param y_test: 真实负荷数据
        :param day_start: 当前天的起始索引
        :param predictions: 已有的预测结果
        """
        if day_start == 0:
            return  # 第一天不需要更新

        # 获取前一天的真实负荷数据
        prev_day_start = day_start - 96
        prev_day_end = day_start

        # 更新滞后特征
        lag_features = ['load_lag_1', 'load_lag_2', 'load_lag_4', 'load_lag_8', 'load_lag_24', 'load_lag_48', 'load_lag_96']

        for i in range(day_start, min(day_start + 96, len(X_test_updated))):
            for lag_col in lag_features:
                if lag_col in X_test_updated.columns:
                    lag_periods = int(lag_col.split('_')[-1])
                    lag_idx = i - lag_periods

                    if lag_idx >= 0:
                        if lag_idx < len(y_test):
                            # 使用真实负荷数据
                            X_test_updated.loc[X_test_updated.index[i], lag_col] = y_test.iloc[lag_idx]
                        elif lag_idx - len(y_test) < len(predictions):
                            # 使用之前的预测数据
                            X_test_updated.loc[X_test_updated.index[i], lag_col] = predictions[lag_idx - len(y_test)]

        # 更新滚动统计特征
        rolling_features = [col for col in X_test_updated.columns if 'rolling' in col or 'ewm' in col]

        for i in range(day_start, min(day_start + 96, len(X_test_updated))):
            for roll_col in rolling_features:
                if roll_col in X_test_updated.columns:
                    # 提取窗口大小
                    if 'rolling' in roll_col:
                        try:
                            window_size = int(roll_col.split('_')[-1])
                        except:
                            continue
                    elif 'ewm' in roll_col:
                        try:
                            window_size = int(roll_col.split('_')[-1])
                        except:
                            continue
                    else:
                        continue

                    # 计算窗口范围
                    window_start = max(0, i - window_size + 1)
                    window_end = i + 1

                    # 收集窗口内的真实/预测负荷数据
                    window_data = []
                    for j in range(window_start, window_end):
                        if j < len(y_test):
                            window_data.append(y_test.iloc[j])
                        elif j - len(y_test) < len(predictions):
                            window_data.append(predictions[j - len(y_test)])

                    if len(window_data) > 0:
                        # 根据特征类型计算统计值
                        if 'mean' in roll_col:
                            X_test_updated.loc[X_test_updated.index[i], roll_col] = np.mean(window_data)
                        elif 'std' in roll_col:
                            X_test_updated.loc[X_test_updated.index[i], roll_col] = np.std(window_data) if len(window_data) > 1 else 0
                        elif 'max' in roll_col:
                            X_test_updated.loc[X_test_updated.index[i], roll_col] = np.max(window_data)
                        elif 'min' in roll_col:
                            X_test_updated.loc[X_test_updated.index[i], roll_col] = np.min(window_data)
                        elif 'ewm' in roll_col:
                            # 简化的指数加权移动平均
                            alpha = 2.0 / (window_size + 1)
                            ewm_val = window_data[0]
                            for val in window_data[1:]:
                                ewm_val = alpha * val + (1 - alpha) * ewm_val
                            X_test_updated.loc[X_test_updated.index[i], roll_col] = ewm_val

        # 更新最近数据权重特征
        recent_features = [col for col in X_test_updated.columns if any(keyword in col for keyword in ['recent_', 'peak_ratio', 'vs_yesterday', 'diff_yesterday'])]

        for i in range(day_start, min(day_start + 96, len(X_test_updated))):
            for recent_col in recent_features:
                if recent_col in X_test_updated.columns:
                    # 这里可以添加更复杂的最近特征更新逻辑
                    # 暂时保持原值，因为这些特征的计算比较复杂
                    pass

    def calculate_accuracy(self, y_true, y_pred, epsilon=0.01):
        """
        计算业务准确率（交易准确率）

        :param y_true: 真实值
        :param y_pred: 预测值
        :param epsilon: 接近零的阈值，默认0.01
        :return: 准确率（0-1之间的浮点数）
        """
        import numpy as np

        # 转换为numpy数组并确保格式一致
        y_true = np.array(y_true).flatten()
        y_pred = np.array(y_pred).flatten()

        # 初始化准确率数组
        accuracies = np.zeros_like(y_true, dtype=float)

        # 判断哪些样本是"真实值和预测值都小于等于 epsilon" → 视为准确预测
        near_zero_mask = (np.abs(y_true) <= epsilon) & (np.abs(y_pred) <= epsilon)
        accuracies[near_zero_mask] = 1.0

        # 对于非零值，使用MAPE的倒数作为准确率
        non_zero_mask = ~near_zero_mask
        if np.sum(non_zero_mask) > 0:
            # 计算相对误差
            relative_errors = np.abs((y_true[non_zero_mask] - y_pred[non_zero_mask]) / y_true[non_zero_mask])
            # 将相对误差转换为准确率：准确率 = 1 - 相对误差
            accuracies[non_zero_mask] = np.maximum(0, 1 - relative_errors)

        # 返回平均准确率
        mean_accuracy = np.mean(accuracies)
        return mean_accuracy

    def save(self, path: str):
        """保存模型（简化实现，仅满足抽象方法要求）"""
        print(f"注意：当前LSTM模型使用训练过程中的自动保存机制，无需手动保存")
        pass

    def load(self, path: str):
        """加载模型（简化实现，仅满足抽象方法要求）"""
        print(f"注意：当前LSTM模型使用训练过程中的自动加载机制，无需手动加载")
        pass





