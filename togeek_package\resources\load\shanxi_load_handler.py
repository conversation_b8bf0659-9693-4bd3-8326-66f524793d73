#!/usr/bin/env python
# -*- coding: UTF-8 -*-
'''
<AUTHOR>
@Date    ：2025/8/5 13:28 
@Info    ：

'''

from tglibs.easy_json import j2o
from togeek_package.lib.request_handler_base import RequestHandlerBase
from togeek_package.load.shanxi_new.main import LoadPredictionSystem

class LoadPredictionSystemHandler(RequestHandlerBase):
    def put(self):

        params = j2o(self.request.body.decode())
        load_data = params.pop('load')
        weather_data = params.pop('weather')
        D = params.pop('D')
        test_days = params.pop('test_days')

        predictor = LoadPredictionSystem(load_data = load_data, weather_data = weather_data)
        result_dict = predictor.run(D = D, test_days = test_days)

        self.write(result_dict)