#!/usr/bin/env python3
"""
测试当前Python环境和chinese_calendar包
"""

import sys
print("=" * 50)
print("Python环境信息:")
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")
print("=" * 50)

try:
    import chinese_calendar as cc
    print("✅ chinese_calendar导入成功!")
    print(f"chinese_calendar版本: {getattr(cc, '__version__', '未知')}")
    
    # 测试功能
    from datetime import date
    today = date.today()
    is_holiday = cc.is_holiday(today)
    print(f"今天({today})是否为节假日: {is_holiday}")
    
except ImportError as e:
    print("❌ chinese_calendar导入失败!")
    print(f"错误信息: {e}")
    print("\n建议解决方案:")
    print("1. 使用 uv run python test_env.py")
    print("2. 或安装包: uv add chinese-calendar")

print("=" * 50)
